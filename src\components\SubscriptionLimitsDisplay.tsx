import React from 'react';
import { useSubscriptionLimits } from '@/hooks/useSubscriptionLimits';
import { useSubscription } from '@/hooks/useSubscription';
import { Progress } from '@/components/ui/progress';

// Fix TypeScript errors with Progress component
interface ProgressProps extends React.HTMLAttributes<HTMLDivElement> {
  value: number;
  indicatorClassName?: string;
}
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { AlertCircle, CheckCircle, Info } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useRouter } from 'next/navigation';

interface SubscriptionLimitsDisplayProps {
  feature: string;
  title: string;
  description?: string;
  showUpgradeButton?: boolean;
}

export function SubscriptionLimitsDisplay({
  feature,
  title,
  description,
  showUpgradeButton = true
}: SubscriptionLimitsDisplayProps) {
  const { checkLimit, isLoading } = useSubscriptionLimits();
  const { planType } = useSubscription();
  const router = useRouter();

  const { hasReached, limit, remaining, hasAccess, usage } = checkLimit(feature);

  // If the feature is boolean (true/false), display a simple check or x
  if (typeof limit === 'boolean') {
    return (
      <Card className="mb-4">
        <CardHeader className="pb-2">
          <CardTitle className="text-lg font-medium flex items-center">
            {title}
            {hasAccess ? (
              <CheckCircle className="ml-2 h-5 w-5 text-green-500" />
            ) : (
              <AlertCircle className="ml-2 h-5 w-5 text-red-500" />
            )}
          </CardTitle>
          {description && <CardDescription>{description}</CardDescription>}
        </CardHeader>
        <CardContent>
          <div className="text-sm text-gray-500">
            {hasAccess ? (
              <span className="text-green-600">Available with your {planType.replace('_', ' ')} plan</span>
            ) : (
              <div className="flex flex-col space-y-2">
                <span className="text-red-600">Not available with your {planType.replace('_', ' ')} plan</span>
                {showUpgradeButton && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => router.push('/pricing')}
                    className="w-fit"
                  >
                    Upgrade to get access
                  </Button>
                )}
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    );
  }

  // For numeric limits, show a progress bar
  const percentUsed = limit > 0 ? Math.min(100, (usage / limit) * 100) : 100;
  const isNearLimit = percentUsed >= 80;
  const isAtLimit = percentUsed >= 100;

  return (
    <Card className="mb-4">
      <CardHeader className="pb-2">
        <CardTitle className="text-lg font-medium">{title}</CardTitle>
        {description && <CardDescription>{description}</CardDescription>}
      </CardHeader>
      <CardContent>
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>
              {isLoading ? 'Loading...' : `${usage} / ${limit} used`}
            </span>
            <span className={isAtLimit ? 'text-red-600 font-medium' : isNearLimit ? 'text-amber-600 font-medium' : 'text-green-600 font-medium'}>
              {isLoading ? '' : isAtLimit ? 'Limit reached' : `${remaining} remaining`}
            </span>
          </div>

          <Progress
            value={percentUsed}
            className={isAtLimit ? 'bg-red-100' : isNearLimit ? 'bg-amber-100' : 'bg-green-100'}
            // @ts-ignore - We've defined the indicatorClassName prop
            indicatorClassName={isAtLimit ? 'bg-red-600' : isNearLimit ? 'bg-amber-600' : 'bg-green-600'}
          />

          {isAtLimit && showUpgradeButton && (
            <div className="mt-2 flex items-start space-x-2">
              <Info className="h-5 w-5 text-blue-500 flex-shrink-0 mt-0.5" />
              <div className="flex flex-col space-y-2">
                <span className="text-sm">You&apos;ve reached your limit for this feature.</span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => router.push('/pricing')}
                  className="w-fit"
                >
                  Upgrade for more
                </Button>
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
