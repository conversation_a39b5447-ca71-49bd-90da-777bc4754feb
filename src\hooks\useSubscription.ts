import { useState, useEffect, useCallback } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { SubscriptionPlan } from '@/lib/subscription';

type FeatureAccessResult = {
  hasAccess: boolean;
  limit: number;
  planType: SubscriptionPlan;
  status: string;
  isLoading: boolean;
  error: string | null;
};

export function useFeatureAccess(featureName: string): FeatureAccessResult {
  const supabase = createClientComponentClient();
  const [result, setResult] = useState<FeatureAccessResult>({
    hasAccess: false,
    limit: 0,
    planType: 'free',
    status: 'inactive',
    isLoading: true,
    error: null,
  });

  useEffect(() => {
    let isMounted = true;

    const checkAccess = async () => {
      try {
        const { data: { session }, error: sessionError } = await supabase.auth.getSession();

        if (sessionError || !session) {
          if (isMounted) {
            setResult({
              hasAccess: false,
              limit: 0,
              planType: 'free',
              status: 'inactive',
              isLoading: false,
              error: sessionError ? sessionError.message : 'No active session',
            });
          }
          return;
        }

        const response = await fetch(`/api/subscription?feature=${encodeURIComponent(featureName)}`);

        if (!response.ok) {
          throw new Error(`Failed to fetch subscription data: ${response.status}`);
        }

        const data = await response.json();

        if (isMounted) {
          setResult({
            hasAccess: data.hasAccess,
            limit: data.limit,
            planType: data.planType,
            status: data.status || 'inactive',
            isLoading: false,
            error: null,
          });
        }
      } catch (error) {
        console.error("Error in useFeatureAccess:", error);
        if (isMounted) {
          setResult({
            hasAccess: false,
            limit: 0,
            planType: 'free',
            status: 'inactive',
            isLoading: false,
            error: error instanceof Error ? error.message : 'Unknown error',
          });
        }
      }
    };

    checkAccess();

    return () => {
      isMounted = false;
    };
  }, [featureName, supabase.auth]);

  return result;
}

type SubscriptionData = {
  planType: SubscriptionPlan;
  status: string;
  currentPeriodEnd?: Date;
  features: Record<string, { hasAccess: boolean; limit?: number }>;
  isLoading: boolean;
  error: string | null;
  refresh: () => Promise<void>; // Added refresh function
};

export function useSubscription(): SubscriptionData {
  const supabase = createClientComponentClient();
  const [subscriptionData, setSubscriptionData] = useState<Omit<SubscriptionData, 'refresh'>>({
    planType: 'free',
    status: 'inactive',
    isLoading: true,
    error: null,
    features: {},
  });
  const [refreshCounter, setRefreshCounter] = useState(0);

  // Function to refresh subscription data
  const refresh = useCallback(() => {
    setRefreshCounter(prev => prev + 1);
  }, []);

  useEffect(() => {
    let isMounted = true;
    let pollingInterval: NodeJS.Timeout | null = null;

    const checkSubscription = async () => {
      try {
        const { data: { session }, error: sessionError } = await supabase.auth.getSession();

        if (sessionError || !session) {
          if (isMounted) {
            setSubscriptionData({
              planType: 'free',
              status: 'inactive',
              isLoading: false,
              error: sessionError ? sessionError.message : 'No active session',
              features: {},
            });
          }
          return;
        }

        // Add a cache-busting parameter to prevent caching
        const timestamp = new Date().getTime();
        const response = await fetch(`/api/subscription?t=${timestamp}`);

        if (!response.ok) {
          throw new Error(`Failed to fetch subscription data: ${response.status}`);
        }

        const data = await response.json();

        if (isMounted) {
          // Check if the plan type has changed
          const newPlanType = data.subscription.planType;
          const oldPlanType = subscriptionData.planType;

          setSubscriptionData({
            planType: newPlanType,
            status: data.subscription.status,
            currentPeriodEnd: data.subscription.currentPeriodEnd
              ? new Date(data.subscription.currentPeriodEnd)
              : undefined,
            features: data.subscription.features || {},
            isLoading: false,
            error: null,
          });

          // Log the subscription data for debugging
          console.log('Subscription data from API:', {
            planType: newPlanType,
            status: data.subscription.status,
            oldPlanType
          });

          // If we're on the free plan but have a subscription ID, set up verification
          // This helps catch subscription updates that might be delayed
          if (newPlanType === 'free' && data.subscription.stripeSubscriptionId) {
            if (!pollingInterval) {
              console.log('Setting up polling for subscription updates');
              pollingInterval = setInterval(() => {
                if (isMounted) {
                  // Use a timestamp to prevent caching
                  const timestamp = new Date().getTime();
                  fetch(`/api/verify-subscription?t=${timestamp}`)
                    .then(response => response.json())
                    .then(verifyData => {
                      console.log('Subscription verification in hook:', verifyData);
                      // Only trigger a refresh if we have verified data that's different
                      if (verifyData.verified && verifyData.planType !== 'free' && verifyData.planType !== newPlanType) {
                        setRefreshCounter(prev => prev + 1);
                      }
                    })
                    .catch(error => {
                      console.error('Error verifying subscription in hook:', error);
                    });
                }
              }, 5000); // Poll every 5 seconds
            }
          } else if (pollingInterval) {
            // If we have a plan or no subscription ID, clear the polling interval
            clearInterval(pollingInterval);
            pollingInterval = null;
          }
        }
      } catch (error) {
        console.error("Error in useSubscription:", error);
        if (isMounted) {
          setSubscriptionData({
            planType: 'free',
            status: 'inactive',
            isLoading: false,
            error: error instanceof Error ? error.message : 'Unknown error',
            features: {},
          });
        }
      }
    };

    checkSubscription();

    return () => {
      isMounted = false;
      if (pollingInterval) {
        clearInterval(pollingInterval);
      }
    };
  }, [supabase.auth, refreshCounter]); // Added refreshCounter to dependencies

  return { ...subscriptionData, refresh };
}