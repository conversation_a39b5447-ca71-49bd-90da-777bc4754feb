import { createClient } from '@supabase/supabase-js';
import { UserUsage } from '../subscription-limits';

// For admin operations that bypass RLS
const getSupabaseAdminClient = () => {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
  const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || '';
  
  if (!supabaseUrl || !supabaseKey) {
    console.error('Missing Supabase credentials');
    throw new Error('Missing Supabase credentials');
  }
  
  return createClient(supabaseUrl, supabaseKey);
};

// Get the current usage for a specific feature
export async function getUserUsage(userId: string): Promise<UserUsage> {
  try {
    const supabase = getSupabaseAdminClient();
    
    // Get the current month and year
    const now = new Date();
    const currentMonth = now.getMonth() + 1; // JavaScript months are 0-indexed
    const currentYear = now.getFullYear();
    
    // Query the usage table for the current month
    const { data, error } = await supabase
      .from('user_usage')
      .select('*')
      .eq('user_id', userId)
      .eq('month', currentMonth)
      .eq('year', currentYear)
      .single();
    
    if (error) {
      console.error('Error fetching user usage:', error);
      // If no record exists, return default values
      if (error.code === 'PGRST116') {
        return {
          coldCallScripts: 0,
          tokens: 0,
          jobApplications: 0,
          aiResumeScorer: 0,
          automatedEmails: 0
        };
      }
      throw error;
    }
    
    return {
      coldCallScripts: data?.cold_call_scripts || 0,
      tokens: data?.tokens || 0,
      jobApplications: data?.job_applications || 0,
      aiResumeScorer: data?.ai_resume_scorer || 0,
      automatedEmails: data?.automated_emails || 0
    };
  } catch (error) {
    console.error('Error in getUserUsage:', error);
    // Return default values on error
    return {
      coldCallScripts: 0,
      tokens: 0,
      jobApplications: 0,
      aiResumeScorer: 0,
      automatedEmails: 0
    };
  }
}

// Increment usage for a specific feature
export async function incrementUserUsage(
  userId: string,
  feature: keyof UserUsage,
  amount: number = 1
): Promise<boolean> {
  try {
    const supabase = getSupabaseAdminClient();
    
    // Get the current month and year
    const now = new Date();
    const currentMonth = now.getMonth() + 1; // JavaScript months are 0-indexed
    const currentYear = now.getFullYear();
    
    // Map the feature name to the database column name
    const columnMap: Record<keyof UserUsage, string> = {
      coldCallScripts: 'cold_call_scripts',
      tokens: 'tokens',
      jobApplications: 'job_applications',
      aiResumeScorer: 'ai_resume_scorer',
      automatedEmails: 'automated_emails'
    };
    
    const columnName = columnMap[feature];
    if (!columnName) {
      throw new Error(`Invalid feature: ${feature}`);
    }
    
    // Check if a record exists for the current month
    const { data, error } = await supabase
      .from('user_usage')
      .select('id')
      .eq('user_id', userId)
      .eq('month', currentMonth)
      .eq('year', currentYear)
      .single();
    
    if (error) {
      // If no record exists, create one
      if (error.code === 'PGRST116') {
        const newRecord: any = {
          user_id: userId,
          month: currentMonth,
          year: currentYear
        };
        newRecord[columnName] = amount;
        
        const { error: insertError } = await supabase
          .from('user_usage')
          .insert([newRecord]);
        
        if (insertError) {
          console.error('Error creating usage record:', insertError);
          throw insertError;
        }
        
        return true;
      }
      
      console.error('Error checking for usage record:', error);
      throw error;
    }
    
    // If a record exists, increment the usage
    const { error: updateError } = await supabase
      .from('user_usage')
      .update({ [columnName]: supabase.sql`${columnName} + ${amount}` })
      .eq('id', data.id);
    
    if (updateError) {
      console.error('Error updating usage record:', updateError);
      throw updateError;
    }
    
    return true;
  } catch (error) {
    console.error(`Error in incrementUserUsage for ${feature}:`, error);
    return false;
  }
}

// Reset usage for a specific feature
export async function resetUserUsage(
  userId: string,
  feature?: keyof UserUsage
): Promise<boolean> {
  try {
    const supabase = getSupabaseAdminClient();
    
    // Get the current month and year
    const now = new Date();
    const currentMonth = now.getMonth() + 1; // JavaScript months are 0-indexed
    const currentYear = now.getFullYear();
    
    // If a specific feature is provided, reset only that feature
    if (feature) {
      const columnMap: Record<keyof UserUsage, string> = {
        coldCallScripts: 'cold_call_scripts',
        tokens: 'tokens',
        jobApplications: 'job_applications',
        aiResumeScorer: 'ai_resume_scorer',
        automatedEmails: 'automated_emails'
      };
      
      const columnName = columnMap[feature];
      if (!columnName) {
        throw new Error(`Invalid feature: ${feature}`);
      }
      
      const { error } = await supabase
        .from('user_usage')
        .update({ [columnName]: 0 })
        .eq('user_id', userId)
        .eq('month', currentMonth)
        .eq('year', currentYear);
      
      if (error) {
        console.error('Error resetting usage:', error);
        throw error;
      }
    } else {
      // If no specific feature is provided, reset all features
      const { error } = await supabase
        .from('user_usage')
        .update({
          cold_call_scripts: 0,
          tokens: 0,
          job_applications: 0,
          ai_resume_scorer: 0,
          automated_emails: 0
        })
        .eq('user_id', userId)
        .eq('month', currentMonth)
        .eq('year', currentYear);
      
      if (error) {
        console.error('Error resetting all usage:', error);
        throw error;
      }
    }
    
    return true;
  } catch (error) {
    console.error('Error in resetUserUsage:', error);
    return false;
  }
}
