import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useSubscriptionLimits } from '@/hooks/useSubscriptionLimits';
import { useSubscription } from '@/hooks/useSubscription';
import { Progress } from '@/components/ui/progress';
import { Button } from '@/components/ui/button';
import { useRouter } from 'next/navigation';
import { CheckCircle, AlertCircle, RefreshCw } from 'lucide-react';

// Fix TypeScript errors with Progress component
interface ProgressProps extends React.HTMLAttributes<HTMLDivElement> {
  value: number;
  indicatorClassName?: string;
}

export function SubscriptionLimitsDashboard() {
  const { usage, checkLimit, refreshUsage, isLoading } = useSubscriptionLimits();
  const { planType } = useSubscription();
  const router = useRouter();

  const features = [
    { id: 'coldCallScripts', name: 'Cold Call Scripts', description: 'AI-generated scripts for reaching out to employers' },
    { id: 'nonmedECs', name: 'Non-Medical ECs', description: 'Non-medical extracurricular opportunities' },
    { id: 'medicalECs', name: 'Medical ECs', description: 'Medical extracurricular opportunities' },
    { id: 'aiResumeScorer', name: 'AI Resume Scorer', description: 'Resume analysis and feedback' },
    { id: 'automatedEmails', name: 'Automated Emails', description: 'Emails sent through the platform' },
  ];

  const booleanFeatures = [
    { id: 'priorityApplicant', name: 'Priority Applicant Status', description: 'Your applications are highlighted to employers' },
    { id: 'customProfileBanner', name: 'Custom Profile Banner', description: 'Personalize your profile with a custom banner' },
    { id: 'aiJobMatching', name: 'AI Job Matching', description: 'Get AI-powered job recommendations' },
    { id: 'newsletter', name: 'Premium Newsletter', description: 'Receive our premium career newsletter' },
  ];

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <div>
          <CardTitle className="text-xl font-medium">Subscription Usage</CardTitle>
          <CardDescription>
            Track your feature usage for your {planType.replace('_', ' ')} plan
          </CardDescription>
        </div>
        <Button
          variant="ghost"
          size="sm"
          onClick={refreshUsage}
          disabled={isLoading}
        >
          <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {features.map(feature => {
            const { hasReached, limit, remaining, hasAccess, usage: featureUsage } = checkLimit(feature.id);

            if (!hasAccess) {
              return (
                <Card key={feature.id} className="border-dashed">
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium text-gray-500">{feature.name}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-500">Not available</span>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => router.push('/pricing')}
                      >
                        Upgrade
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              );
            }

            const percentUsed = typeof limit === 'number' && limit > 0 ? Math.min(100, (featureUsage / limit) * 100) : 0;
            const isNearLimit = percentUsed >= 80;
            const isAtLimit = percentUsed >= 100;

            return (
              <Card key={feature.id}>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">{feature.name}</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>
                      {isLoading ? 'Loading...' : `${featureUsage} / ${limit} used`}
                    </span>
                    <span className={isAtLimit ? 'text-red-600 font-medium' : isNearLimit ? 'text-amber-600 font-medium' : 'text-green-600 font-medium'}>
                      {isLoading ? '' : isAtLimit ? 'Limit reached' : `${remaining} remaining`}
                    </span>
                  </div>

                  <Progress
                    value={percentUsed}
                    className={isAtLimit ? 'bg-red-100' : isNearLimit ? 'bg-amber-100' : 'bg-green-100'}
                    // @ts-ignore - We've defined the indicatorClassName prop
                    indicatorClassName={isAtLimit ? 'bg-red-600' : isNearLimit ? 'bg-amber-600' : 'bg-green-600'}
                  />

                  {isAtLimit && (
                    <div className="flex justify-end">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => router.push('/pricing')}
                      >
                        Upgrade for more
                      </Button>
                    </div>
                  )}
                </CardContent>
              </Card>
            );
          })}
        </div>

        <div>
          <h3 className="text-sm font-medium mb-3">Premium Features</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {booleanFeatures.map(feature => {
              const { hasAccess } = checkLimit(feature.id);

              return (
                <div key={feature.id} className="flex items-start space-x-2">
                  {hasAccess ? (
                    <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
                  ) : (
                    <AlertCircle className="h-5 w-5 text-gray-300 mt-0.5" />
                  )}
                  <div>
                    <h4 className={`text-sm font-medium ${hasAccess ? 'text-gray-900' : 'text-gray-500'}`}>
                      {feature.name}
                    </h4>
                    <p className={`text-xs ${hasAccess ? 'text-gray-600' : 'text-gray-400'}`}>
                      {feature.description}
                    </p>
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {planType !== 'premium_monthly' && planType !== 'premium_yearly' && (
          <div className="flex justify-center">
            <Button onClick={() => router.push('/pricing')}>
              Upgrade Subscription
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
