import { NextRequest, NextResponse } from "next/server";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";
import { createClient } from '@supabase/supabase-js';

// For admin operations that bypass RLS
const getSupabaseAdminClient = () => {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
  const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || '';
  
  if (!supabaseUrl || !supabaseKey) {
    console.error('Missing Supabase credentials');
    throw new Error('Missing Supabase credentials');
  }
  
  return createClient(supabaseUrl, supabaseKey);
};

// Get the current usage for a specific feature
export async function GET(req: NextRequest) {
  try {
    // Get the current user from the session
    const supabase = createRouteHandlerClient({ cookies });
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    
    if (sessionError || !session) {
      return NextResponse.json({ error: "Unauthorized - Please log in" }, { status: 401 });
    }
    
    const userId = session.user.id;
    
    // Get the current month and year
    const now = new Date();
    const currentMonth = now.getMonth() + 1; // JavaScript months are 0-indexed
    const currentYear = now.getFullYear();
    
    // Use admin client to query the database
    const adminClient = getSupabaseAdminClient();
    
    // Query the usage table for the current month
    const { data, error } = await adminClient
      .from('user_usage')
      .select('*')
      .eq('user_id', userId)
      .eq('month', currentMonth)
      .eq('year', currentYear)
      .single();
    
    if (error) {
      // If no record exists, return default values
      if (error.code === 'PGRST116') {
        return NextResponse.json({
          coldCallScripts: 0,
          tokens: 0,
          jobApplications: 0,
          aiResumeScorer: 0,
          automatedEmails: 0,
          nonmedECs: 0,
          medicalECs: 0
        });
      }
      
      console.error('Error fetching user usage:', error);
      return NextResponse.json({ error: "Failed to fetch usage data" }, { status: 500 });
    }
    
    // Return the usage data
    return NextResponse.json({
      coldCallScripts: data?.cold_call_scripts || 0,
      tokens: data?.tokens || 0,
      jobApplications: data?.job_applications || 0,
      aiResumeScorer: data?.ai_resume_scorer || 0,
      automatedEmails: data?.automated_emails || 0,
      nonmedECs: data?.nonmed_ecs || 0,
      medicalECs: data?.medical_ecs || 0
    });
  } catch (error) {
    console.error('Error in GET usage:', error);
    return NextResponse.json({ error: "Server error" }, { status: 500 });
  }
}

// Increment usage for a specific feature
export async function POST(req: NextRequest) {
  try {
    // Get the current user from the session
    const supabase = createRouteHandlerClient({ cookies });
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    
    if (sessionError || !session) {
      return NextResponse.json({ error: "Unauthorized - Please log in" }, { status: 401 });
    }
    
    const userId = session.user.id;
    
    // Get the feature and amount from the request body
    const { feature, amount = 1 } = await req.json();
    
    if (!feature) {
      return NextResponse.json({ error: "Missing feature parameter" }, { status: 400 });
    }
    
    // Map the feature name to the database column name
    const columnMap: Record<string, string> = {
      coldCallScripts: 'cold_call_scripts',
      tokens: 'tokens',
      jobApplications: 'job_applications',
      aiResumeScorer: 'ai_resume_scorer',
      automatedEmails: 'automated_emails',
      nonmedECs: 'nonmed_ecs',
      medicalECs: 'medical_ecs'
    };
    
    const columnName = columnMap[feature];
    if (!columnName) {
      return NextResponse.json({ error: "Invalid feature" }, { status: 400 });
    }
    
    // Get the current month and year
    const now = new Date();
    const currentMonth = now.getMonth() + 1; // JavaScript months are 0-indexed
    const currentYear = now.getFullYear();
    
    // Use admin client to update the database
    const adminClient = getSupabaseAdminClient();
    
    // Check if a record exists for the current month
    const { data, error } = await adminClient
      .from('user_usage')
      .select('id')
      .eq('user_id', userId)
      .eq('month', currentMonth)
      .eq('year', currentYear)
      .single();
    
    if (error) {
      // If no record exists, create one
      if (error.code === 'PGRST116') {
        const newRecord: any = {
          user_id: userId,
          month: currentMonth,
          year: currentYear
        };
        newRecord[columnName] = amount;
        
        const { error: insertError } = await adminClient
          .from('user_usage')
          .insert([newRecord]);
        
        if (insertError) {
          console.error('Error creating usage record:', insertError);
          return NextResponse.json({ error: "Failed to create usage record" }, { status: 500 });
        }
        
        return NextResponse.json({ success: true, feature, amount });
      }
      
      console.error('Error checking for usage record:', error);
      return NextResponse.json({ error: "Failed to check for usage record" }, { status: 500 });
    }
    
    // If a record exists, increment the usage
    const { error: updateError } = await adminClient
      .from('user_usage')
      .update({ [columnName]: adminClient.sql`${columnName} + ${amount}` })
      .eq('id', data.id);
    
    if (updateError) {
      console.error('Error updating usage record:', updateError);
      return NextResponse.json({ error: "Failed to update usage record" }, { status: 500 });
    }
    
    return NextResponse.json({ success: true, feature, amount });
  } catch (error) {
    console.error('Error in POST usage:', error);
    return NextResponse.json({ error: "Server error" }, { status: 500 });
  }
}
