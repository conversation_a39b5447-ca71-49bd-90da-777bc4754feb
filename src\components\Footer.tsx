"use client";
import React, { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { Instagram, Linkedin, Coffee, MessageSquare, Lightbulb } from 'lucide-react';
import { FaDiscord } from 'react-icons/fa';
import { TextHoverEffect } from './ui/text-hover-effect';
import FeedbackModal from './FeedbackModal';

export default function Footer({ className = '' }) {
    const [mousePos, setMousePos] = useState({ x: 0, y: 0 });
    const [isFeedbackModalOpen, setIsFeedbackModalOpen] = useState(false);

    function handleMouseMove(e: { clientX: any; clientY: any; }) {
        setMousePos({ x: e.clientX, y: e.clientY });
    }
    const openFeedbackModal = () => {
      setIsFeedbackModalOpen(true);
    };
    
  return (
    <footer 
      className={`relative py-16 overflow-hidden ${className}`}
      onMouseMove={handleMouseMove}
    >

    <FeedbackModal isOpen={isFeedbackModalOpen} setIsOpen={setIsFeedbackModalOpen} />
      <div className="absolute inset-0 flex items-center justify-center opacity-[0.15]">
        <Image 
          src="/klinnfooterlogo.png" 
          alt="Klinn Logo" 
          width={500}
          height={300}
          className="max-w-full h-auto"
        />
      </div>
      
      <div className="absolute inset-0 flex items-center justify-center opacity-[0.8] max-md:invisible">
        <TextHoverEffect text="klinn" mousePos={mousePos} />
      </div>
      
      <div className="container mx-auto relative z-10">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-16">
          {/* Connect With Us Section */}
          <div className="space-y-6">
            <h3 className="text-base font-medium tracking-wider text-white uppercase">Connect</h3>
            <div className="flex items-center gap-5">
              <a
                href="https://www.instagram.com/klinnworks"
                target="_blank"
                rel="noopener noreferrer"
                aria-label="Instagram"
                className="group transition-transform duration-300 hover:scale-110"
              >
                <Instagram className="w-5 h-5 text-white hover:text-gray-300" />
              </a>
              
              <a
                href="https://www.linkedin.com/company/klinn"
                target="_blank"
                rel="noopener noreferrer"
                aria-label="LinkedIn"
                className="group transition-transform duration-300 hover:scale-110"
              >
                <Linkedin className="w-5 h-5 text-white hover:text-gray-300" />
              </a>
              
              <a
                href="https://discord.gg/N6h4z8S8a6"
                target="_blank"
                rel="noopener noreferrer"
                aria-label="Discord"
                className="group transition-transform duration-300 hover:scale-110"
              >
                <FaDiscord className="w-5 h-5 text-white hover:text-gray-300" />
              </a>
            </div>
          </div>
          
          {/* Support Section */}
          <div className="space-y-6 flex flex-col items-start md:items-end">
            <h3 className="text-base font-medium tracking-wider text-white uppercase">Support</h3>
            <div className="flex flex-col space-y-4 items-start md:items-end">
              <a
                href="https://buymeacoffee.com/klinn.works"
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center text-sm font-medium text-yellow-300 hover:text-yellow-400 transition-colors duration-300"
              >
                <Coffee className="w-4 h-4 mr-2" />
                Buy me a coffee
              </a>
              
              <Link href="/testimonials">
                <span className="mt-1 inline-flex items-center text-sm font-medium text-white hover:text-gray-300 transition-colors duration-300">
                  <MessageSquare className="w-4 h-4 mr-2" />
                  Testimonials
                </span>
              </Link>

              <button
                onClick={openFeedbackModal}
              >
                <span className="-mt-16 inline-flex items-center text-sm font-medium text-white hover:text-gray-300 transition-colors duration-300">
                  <Lightbulb className="w-4 h-4 mr-2" />
                  Share feedback
                </span>
              </button>
            </div>
          </div>
        </div>
        
        <div className="max-md:mt-12 mt-24 pt-4 border-t border-gray-100 flex flex-col md:flex-row justify-between items-center">
          <div className="text-xs text-white font-light tracking-wide">
            © {new Date().getFullYear()} Klinn LLC. All rights reserved.
          </div>
          
          <div className="flex space-x-8 mt-4 md:mt-0">
            <Link href="/customer-care/terms-of-service">
              <span className="text-xs text-white hover:text-gray-300 transition-colors tracking-wide">
                Terms of Service
              </span>
            </Link>
            
            <Link href="/customer-care/privacy-policy">
              <span className="text-xs text-white hover:text-gray-300 transition-colors tracking-wide">
                Privacy Policy
              </span>
            </Link>
          </div>
        </div>
      </div>
    </footer>
  );
}