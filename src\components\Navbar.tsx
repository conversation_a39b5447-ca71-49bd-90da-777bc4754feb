"use client";

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Stethoscope,
  GraduationCap,
  BookOpen,
  Menu,
  X,
  LogIn,
  LogOut,
  User,
  Glasses,
  Briefcase,
  ChevronDown,
  ChevronUp,
} from 'lucide-react';
import { useAuth } from '../context/AuthContext';
import clsx from 'clsx';
import CustomNavDropdown from './CustomNavDropdown';

// ================== NavLink ======================
interface NavLinkProps {
  href: string;
  icon: React.ComponentType<any>;
  children: React.ReactNode;
  onClick?: () => void;
  disabled?: boolean;
  className?: string;
  isScrolled?: boolean;
  isMobile?: boolean;
}

/**
 * A single navigation link (for both mobile & desktop).
 */
const NavLink: React.FC<NavLinkProps> = ({
  href,
  icon: Icon,
  children,
  onClick,
  disabled,
  className,
  isScrolled = false,
  isMobile = false,
}) => (
  <Link href={href} passHref>
    <Button
      variant="link"
      disabled={disabled}
      onClick={onClick}
      className={clsx(
        isScrolled
          ? "text-blue-900 hover:text-blue-700"
          : "text-white hover:text-white/80",
        "transition-all duration-300 flex items-center gap-1 font-light tracking-wide px-2 py-1", 
        "text-sm lg:text-sm",
        className
      )}
    >
      <Icon className="flex-shrink-0 h-3.5 w-3.5 opacity-70" />
      <span className="relative">
        {children}
        {!isMobile && (
          <span className={clsx(
            "absolute inset-x-0 bottom-0 h-px transform scale-x-0 transition-transform duration-300 ease-out",
            isScrolled ? "bg-blue-700" : "bg-white",
            "group-hover:scale-x-100 origin-left"
          )}></span>
        )}
      </span>
    </Button>
  </Link>
);

// Define NavDropdownProps
interface NavDropdownProps {
  title: string;
  icon: React.ComponentType<any>;
  items: Array<{
    href: string;
    label: string;
    disabled?: boolean;
  }>;
  closeMenu: () => void;
  isMobile?: boolean;
  isScrolled?: boolean;
}

const NavDropdown: React.FC<NavDropdownProps> = ({
  title,
  icon: Icon,
  items,
  closeMenu,
  isMobile = false,
  isScrolled = false,
}) => {
  const [isOpen, setIsOpen] = useState(false);

  const toggleOpen = () => setIsOpen(!isOpen);

  // Mobile
  if (isMobile) {
    return (
      <div className="w-full">
        <Button
          variant="link"
          className={clsx(
            "flex items-center justify-between w-full transition-all duration-300 font-light px-2 py-1",
            isScrolled
              ? "text-blue-900 hover:text-blue-700"
              : "text-white hover:text-white/80"
          )}
          onClick={toggleOpen}
        >
          <div className="flex items-center gap-1">
            <Icon className="flex-shrink-0 h-3.5 w-3.5 opacity-70" />
            <span className="text-sm">{title}</span>
          </div>
          {isOpen ? <ChevronUp className="h-3 w-3" /> : <ChevronDown className="h-3 w-3" />}
        </Button>

        {isOpen && (
          <div className="pl-6 mt-1 space-y-1 overflow-hidden transition-all duration-300 ease-in-out" 
               style={{ animation: 'fadeIn 0.3s ease-out, slideDown 0.3s ease-out' }}>
            {items.map((item, index) => (
              <Link
                key={index}
                href={item.href}
                className={clsx(
                  "block text-xs font-light tracking-wide py-1 transition-all duration-300",
                  isScrolled
                    ? "text-blue-900/90 hover:text-blue-700"
                    : "text-white/90 hover:text-white"
                )}
                onClick={() => {
                  if (!item.disabled) closeMenu();
                  setIsOpen(false);
                }}
              >
                {item.label}
              </Link>
            ))}
          </div>
        )}
      </div>
    );
  }

  // Desktop
  return (
    <div className="relative inline-block">
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="link"
            className={clsx(
              "flex items-center gap-1 font-light tracking-wide text-sm transition-all duration-300 h-8",
              isScrolled
                ? "text-blue-900 hover:text-blue-700"
                : "text-white hover:text-white/80"
            )}
          >
            <Icon className="flex-shrink-0 h-3.5 w-3.5 opacity-70" />
            {title}
            <ChevronDown className={clsx(
              "h-3 w-3 transition-transform duration-300",
              isOpen && "transform rotate-180"
            )} />
          </Button>
        </DropdownMenuTrigger>

        <DropdownMenuContent 
          className="absolute mt-1 w-44 bg-white/95 backdrop-blur-lg text-black py-1 z-50 rounded-md shadow-md border-0"
          sideOffset={4}
          align="center"
          avoidCollisions={true}
          collisionPadding={8}
          style={{ 
            transform: 'translateX(-50%)', 
            left: '50%',
            animation: 'fadeIn 0.3s ease-out forwards',
            position: 'absolute'
          }}
        >
          <div className="absolute top-0 left-1/2 -translate-x-1/2 -translate-y-2 w-2 h-2 rotate-45 bg-white/95"></div>
          {items.map((item, index) => (
            <DropdownMenuItem
              key={index}
              disabled={item.disabled}
              className="cursor-pointer hover:bg-gray-50/80 p-0 transition-colors duration-300"
            >
              <Link
                href={item.href}
                className="block w-full px-3 py-1.5 text-xs font-light text-gray-800 hover:text-black transition-colors duration-300"
                onClick={closeMenu}
              >
                {item.label}
              </Link>
            </DropdownMenuItem>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
};

// ================== Navbar Component ======================
const Navbar: React.FC = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const { user, logout } = useAuth();

  useEffect(() => {
    const style = document.createElement('style');
    style.innerHTML = `
      @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
      }
      @keyframes slideDown {
        from { transform: translateY(-10px); }
        to { transform: translateY(0); }
      }
      @keyframes slideInFromTop {
        from { transform: translateY(-20px); opacity: 0; }
        to { transform: translateY(0); opacity: 1; }
      }
      @keyframes slideOutToTop {
        from { transform: translateY(0); opacity: 1; }
        to { transform: translateY(-20px); opacity: 0; }
      }
    `;
    document.head.appendChild(style);
    return () => {
      document.head.removeChild(style);
    };
  }, []);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 20); 
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  useEffect(() => {
    if (isMenuOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }
    
    return () => {
      document.body.style.overflow = '';
    };
  }, [isMenuOpen]);

  const toggleMenu = () => setIsMenuOpen(!isMenuOpen);
  const closeMenu = () => setIsMenuOpen(false);

  const handleLogout = async () => {
    try {
      await logout();
      console.log("Signed out");
      closeMenu();
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  return (
    <>
      <nav
        className={clsx(
          "fixed top-0 left-0 right-0 z-50 w-full transition-all duration-300 ease-in-out px-3 lg:px-5",
          isScrolled
            ? "bg-white/90 backdrop-blur-lg shadow-sm text-blue-900 h-14"
            : "bg-transparent/10 backdrop-blur-sm lg:bg-transparent text-white h-14 lg:h-16"
        )}
      >
        <div className="max-w-6xl mx-auto flex items-center justify-between h-full">
          {/* logo */}
          <Link href="/" className="flex items-center gap-1.5 text-xl font-medium">
            <Image
              src={isScrolled ? "/images/KlinnLogo.png" : "/images/KlinnLogo2.png"}
              alt="Klinn Logo"
              width={24}
              height={24}
              className="transition-transform duration-300 hover:scale-105"
            />
            <span
              className={clsx(
                "transition-colors duration-300",
                isScrolled
                  ? "text-blue-900 hover:text-blue-700"
                  : "text-white hover:text-white/80"
              )}
            >
              klinn
            </span>
          </Link>
          {/* Mobile Menu */}
          <Button
            variant="ghost"
            className={clsx(
              "transition-all duration-300 lg:hidden p-1 rounded-full",
              isScrolled ? "text-blue-900 hover:bg-blue-50" : "text-white hover:bg-white/10"
            )}
            onClick={toggleMenu}
          >
            {isMenuOpen ? <X className="h-4 w-4" /> : <Menu className="h-4 w-4" />}
          </Button>

          {/* Desktop Nav Items */}
          <div className="hidden lg:flex items-center gap-3 h-8">
            {/* Clinic */}
            <NavLink href="/outreach" icon={Glasses} isScrolled={isScrolled}>
              Outreach
            </NavLink>

            {/* Jobs */}
            <NavLink href="/clinic-openings" icon={Briefcase} isScrolled={isScrolled}>
              Jobs
            </NavLink>

            <CustomNavDropdown
              title="Extracurriculars"
              icon={BookOpen}
              items={[
                { href: '/extracurriculars', label: 'Medical' },
                { href: '/extracurriculars/non-medical', label: 'Non-Medical' },
              ]}
              isScrolled={isScrolled}
            />

            <NavLink href="/ai-consulting" icon={GraduationCap} isScrolled={isScrolled}>
              AI Consulting
            </NavLink>

            {/* divider */}
            <div className="h-4 border-l border-current opacity-20 mx-1"></div>

            {/* User Profile / Authentication with Post a Job link */}
            {user ? (
              <div className="flex items-center gap-2 h-8">
                <CustomNavDropdown
                  title="Profile"
                  icon={User}
                  items={[
                    { href: '/profile-dashboard', label: 'View Profile' },
                    { label: 'Logout', icon: LogOut, onClick: handleLogout }
                  ]}
                  isScrolled={isScrolled}
                  width="w-36"
                />
            
                {/* Post a Job Link */}
                <NavLink 
                  href="/job-posting" 
                  icon={Briefcase} 
                  isScrolled={isScrolled}
                  className={clsx(
                    "ml-1",
                    isScrolled ? "bg-blue-50 hover:bg-blue-100" : "bg-white/10 hover:bg-white/20"
                  )}
                >
                  Post a Job
                </NavLink>
              </div>
            ) : (
              <NavLink 
                href="/auth" 
                icon={LogIn} 
                isScrolled={isScrolled}
                className={clsx(
                  isScrolled ? "bg-blue-50 hover:bg-blue-100" : "bg-white/10 hover:bg-white/20"
                )}
              >
                Login
              </NavLink>
            )}
          </div>
        </div>
      </nav>

      {/* Mobile Nav Items - Fullscreen with subtle blur */}
      {isMenuOpen && (
        <div className="fixed inset-0 z-40 lg:hidden flex flex-col">
          {/* Overlay backdrop with subtle blur */}
          <div 
            className="absolute inset-0 bg-black/30 backdrop-blur-sm" 
            onClick={closeMenu}
            style={{ animation: 'fadeIn 0.3s ease-out' }}
          ></div>
          
          {/* Actual mobile menu - matching navbar styling */}
          <div 
            className={clsx(
              "relative mt-14 max-h-[calc(100vh-3.5rem)] overflow-y-auto",
              isScrolled 
                ? "bg-white/90 backdrop-blur-lg text-blue-900" 
                : "bg-blue-900/30 backdrop-blur-lg text-white",
              "shadow-lg"
            )}
            style={{ animation: 'slideInFromTop 0.3s ease-out' }}
          >
            <div className="px-4 py-4 flex flex-col space-y-4">
              <NavLink 
                href="/outreach" 
                icon={Glasses} 
                onClick={closeMenu} 
                isScrolled={isScrolled} 
                isMobile={true}
                className="py-2 hover:bg-black/5 rounded-md"
              >
                Outreach
              </NavLink>
              
              <NavLink 
                href="/clinic-openings" 
                icon={Briefcase} 
                onClick={closeMenu} 
                isScrolled={isScrolled} 
                isMobile={true}
                className="py-2 hover:bg-black/5 rounded-md"
              >
                Jobs
              </NavLink>
              
              {/* ec dropdown */}
              <NavDropdown
                title="Extracurriculars"
                icon={BookOpen}
                items={[
                  { href: '/extracurriculars', label: 'Medical' },
                  { href: '/extracurriculars/non-medical', label: 'Non-Medical' },
                ]}
                closeMenu={closeMenu}
                isMobile={true}
                isScrolled={isScrolled}
              />
              
              <NavLink 
                href="/ai-consulting" 
                icon={GraduationCap} 
                onClick={closeMenu} 
                isScrolled={isScrolled} 
                isMobile={true}
                className="py-2 hover:bg-black/5 rounded-md"
              >
                AI Consulting
              </NavLink>
              
              {/* divider */}
              <div className={clsx(
                "h-px w-full my-2",
                isScrolled ? "bg-blue-900/10" : "bg-white/20"
              )}></div>
              
              {user ? (
                <div className="flex flex-col space-y-4">
                  <NavLink
                    href="/profile-dashboard"
                    icon={User}
                    onClick={closeMenu}
                    isScrolled={isScrolled}
                    isMobile={true}
                    className="py-2 hover:bg-black/5 rounded-md"
                  >
                    Profile
                  </NavLink>
                  
                  <NavLink 
                    href="/job-posting" 
                    icon={Briefcase} 
                    onClick={closeMenu} 
                    isScrolled={isScrolled}
                    isMobile={true}
                    className={clsx(
                      "py-2 rounded-md",
                      isScrolled 
                        ? "bg-blue-50 hover:bg-blue-100" 
                        : "bg-white/10 hover:bg-white/20"
                    )}
                  >
                    Post a Job
                  </NavLink>
                  
                  <Button
                    variant="ghost"
                    className={clsx(
                      "flex items-center justify-center gap-1 font-light py-2 rounded-md mt-2 transition-all duration-300",
                      isScrolled 
                        ? "text-red-600 hover:bg-red-50" 
                        : "text-red-300 hover:bg-white/10"
                    )}
                    onClick={handleLogout}
                  >
                    <LogOut className="h-3.5 w-3.5 opacity-80" />
                    <span>Logout</span>
                  </Button>
                </div>
              ) : (
                <NavLink
                  href="/auth"
                  icon={LogIn}
                  onClick={closeMenu}
                  isScrolled={isScrolled}
                  isMobile={true}
                  className={clsx(
                    "py-2 rounded-md justify-center",
                    isScrolled 
                      ? "bg-blue-50 hover:bg-blue-100" 
                      : "bg-white/10 hover:bg-white/20"
                  )}
                >
                  Login
                </NavLink>
              )}
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default Navbar;